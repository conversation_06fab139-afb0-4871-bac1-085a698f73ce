#!/usr/bin/env tsx

/**
 * Cleanup Words Without Definitions Script
 *
 * This script removes all words from the database that don't have any definitions.
 * It also handles cleanup of related data like LastSeenWord entries and removes
 * these words from collections.
 *
 * Usage:
 *   yarn cleanup:words-no-definitions [options]
 *
 * Options:
 *   --dry-run          Show what would be deleted without actually deleting
 *   --batch-size=N     Process N words at a time (default: 100)
 *   --language=LANG    Only process words in specific language (EN/VI)
 *   --force            Skip confirmation prompt
 *
 * Examples:
 *   yarn cleanup:words-no-definitions --dry-run
 *   yarn cleanup:words-no-definitions --language=EN --batch-size=50
 *   yarn cleanup:words-no-definitions --force
 */

import { PrismaClient } from '@prisma/client';
import { confirm } from '@inquirer/prompts';

interface CleanupOptions {
	dryRun: boolean;
	batchSize: number;
	language?: 'EN' | 'VI';
	force: boolean;
}

interface CleanupStats {
	totalWordsChecked: number;
	wordsWithoutDefinitions: number;
	wordsDeleted: number;
	collectionsUpdated: number;
	lastSeenWordsDeleted: number;
	errors: string[];
}

class WordCleanupService {
	private prisma: PrismaClient;
	private stats: CleanupStats;

	constructor() {
		this.prisma = new PrismaClient();
		this.stats = {
			totalWordsChecked: 0,
			wordsWithoutDefinitions: 0,
			wordsDeleted: 0,
			collectionsUpdated: 0,
			lastSeenWordsDeleted: 0,
			errors: [],
		};
	}

	async cleanup(options: CleanupOptions): Promise<CleanupStats> {
		console.log('🧹 Starting word cleanup process...');
		console.log(`📊 Options: ${JSON.stringify(options, null, 2)}`);

		try {
			await this.prisma.$connect();
			console.log('📦 Connected to database');

			// Find words without definitions
			const wordsWithoutDefinitions = await this.findWordsWithoutDefinitions(options);

			if (wordsWithoutDefinitions.length === 0) {
				console.log('✅ No words without definitions found!');
				return this.stats;
			}

			console.log(`🔍 Found ${wordsWithoutDefinitions.length} words without definitions`);

			if (options.dryRun) {
				await this.performDryRun(wordsWithoutDefinitions);
				return this.stats;
			}

			if (!options.force) {
				const shouldContinue = await confirm({
					message: `Are you sure you want to delete ${wordsWithoutDefinitions.length} words without definitions?`,
					default: false,
				});

				if (!shouldContinue) {
					console.log('❌ Operation cancelled by user');
					return this.stats;
				}
			}

			// Perform actual cleanup
			await this.performCleanup(wordsWithoutDefinitions, options);
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';
			console.error('❌ Cleanup failed:', errorMessage);
			this.stats.errors.push(errorMessage);
		} finally {
			await this.prisma.$disconnect();
			console.log('📦 Disconnected from database');
		}

		return this.stats;
	}

	private async findWordsWithoutDefinitions(options: CleanupOptions) {
		console.log('🔍 Searching for words without definitions...');

		const whereClause: any = {
			definitions: {
				none: {},
			},
		};

		if (options.language) {
			whereClause.language = options.language;
		}

		const words = await this.prisma.word.findMany({
			where: whereClause,
			select: {
				id: true,
				term: true,
				language: true,
				created_at: true,
			},
			orderBy: {
				created_at: 'asc',
			},
		});

		this.stats.totalWordsChecked = await this.prisma.word.count({
			where: options.language ? { language: options.language } : undefined,
		});
		this.stats.wordsWithoutDefinitions = words.length;

		return words;
	}

	private async performDryRun(words: any[]) {
		console.log('\n🔍 DRY RUN - No actual changes will be made\n');

		console.log('📋 Words that would be deleted:');
		console.log('================================');

		for (const word of words.slice(0, 10)) {
			console.log(`- ${word.term} (${word.language}) - ID: ${word.id}`);
		}

		if (words.length > 10) {
			console.log(`... and ${words.length - 10} more words`);
		}

		// Check collections that would be affected (in batches to avoid bind variable limit)
		const affectedCollections = await this.findAffectedCollectionsBatched(
			words.map((w) => w.id)
		);
		console.log(`\n📚 Collections that would be updated: ${affectedCollections.length}`);

		// Check LastSeenWord entries that would be deleted (in batches)
		const lastSeenCount = await this.countLastSeenWordsBatched(words.map((w) => w.id));
		console.log(`🔄 LastSeenWord entries that would be deleted: ${lastSeenCount}`);
	}

	private async performCleanup(words: any[], options: CleanupOptions) {
		console.log('\n🗑️ Starting actual cleanup...');

		const wordIds = words.map((w) => w.id);
		const batches = this.createBatches(wordIds, options.batchSize);

		for (let i = 0; i < batches.length; i++) {
			const batch = batches[i];
			console.log(`📦 Processing batch ${i + 1}/${batches.length} (${batch.length} words)`);

			try {
				await this.processBatch(batch);
			} catch (error) {
				const errorMessage = `Batch ${i + 1} failed: ${
					error instanceof Error ? error.message : 'Unknown error'
				}`;
				console.error(`❌ ${errorMessage}`);
				this.stats.errors.push(errorMessage);
			}
		}
	}

	private async processBatch(wordIds: string[]) {
		await this.prisma.$transaction(async (tx) => {
			// 1. Delete LastSeenWord entries
			const deletedLastSeenWords = await tx.lastSeenWord.deleteMany({
				where: {
					word_id: {
						in: wordIds,
					},
				},
			});
			this.stats.lastSeenWordsDeleted += deletedLastSeenWords.count;

			// 2. Remove words from collections
			const collectionsToUpdate = await tx.collection.findMany({
				where: {
					word_ids: {
						hasSome: wordIds,
					},
				},
				select: {
					id: true,
					word_ids: true,
				},
			});

			for (const collection of collectionsToUpdate) {
				const updatedWordIds = collection.word_ids.filter((id) => !wordIds.includes(id));
				await tx.collection.update({
					where: { id: collection.id },
					data: { word_ids: updatedWordIds },
				});
			}
			this.stats.collectionsUpdated += collectionsToUpdate.length;

			// 3. Delete the words
			const deletedWords = await tx.word.deleteMany({
				where: {
					id: {
						in: wordIds,
					},
				},
			});
			this.stats.wordsDeleted += deletedWords.count;
		});
	}

	private async findAffectedCollections(wordIds: string[]) {
		return await this.prisma.collection.findMany({
			where: {
				word_ids: {
					hasSome: wordIds,
				},
			},
			select: {
				id: true,
				name: true,
				word_ids: true,
			},
		});
	}

	private async findAffectedCollectionsBatched(wordIds: string[]) {
		const batchSize = 1000; // Safe batch size for PostgreSQL
		const allCollections = new Map<string, any>();

		for (let i = 0; i < wordIds.length; i += batchSize) {
			const batch = wordIds.slice(i, i + batchSize);
			const collections = await this.prisma.collection.findMany({
				where: {
					word_ids: {
						hasSome: batch,
					},
				},
				select: {
					id: true,
					name: true,
					word_ids: true,
				},
			});

			collections.forEach((collection) => {
				allCollections.set(collection.id, collection);
			});
		}

		return Array.from(allCollections.values());
	}

	private async countLastSeenWordsBatched(wordIds: string[]): Promise<number> {
		const batchSize = 1000; // Safe batch size for PostgreSQL
		let totalCount = 0;

		for (let i = 0; i < wordIds.length; i += batchSize) {
			const batch = wordIds.slice(i, i + batchSize);
			const count = await this.prisma.lastSeenWord.count({
				where: {
					word_id: {
						in: batch,
					},
				},
			});
			totalCount += count;
		}

		return totalCount;
	}

	private createBatches<T>(items: T[], batchSize: number): T[][] {
		const batches: T[][] = [];
		for (let i = 0; i < items.length; i += batchSize) {
			batches.push(items.slice(i, i + batchSize));
		}
		return batches;
	}

	printStats() {
		console.log('\n📊 Cleanup Statistics:');
		console.log('=======================');
		console.log(`Total words checked: ${this.stats.totalWordsChecked}`);
		console.log(`Words without definitions found: ${this.stats.wordsWithoutDefinitions}`);
		console.log(`Words deleted: ${this.stats.wordsDeleted}`);
		console.log(`Collections updated: ${this.stats.collectionsUpdated}`);
		console.log(`LastSeenWord entries deleted: ${this.stats.lastSeenWordsDeleted}`);

		if (this.stats.errors.length > 0) {
			console.log(`\n❌ Errors encountered: ${this.stats.errors.length}`);
			this.stats.errors.forEach((error, index) => {
				console.log(`  ${index + 1}. ${error}`);
			});
		}
	}
}

async function main() {
	const args = process.argv.slice(2);

	const options: CleanupOptions = {
		dryRun: args.includes('--dry-run'),
		batchSize: parseInt(
			args.find((arg) => arg.startsWith('--batch-size='))?.split('=')[1] || '100'
		),
		language: args.find((arg) => arg.startsWith('--language='))?.split('=')[1] as
			| 'EN'
			| 'VI'
			| undefined,
		force: args.includes('--force'),
	};

	const cleanupService = new WordCleanupService();
	const stats = await cleanupService.cleanup(options);
	cleanupService.printStats();

	if (stats.errors.length > 0) {
		process.exit(1);
	}
}

if (require.main === module) {
	main().catch((error) => {
		console.error('💥 Script failed:', error);
		process.exit(1);
	});
}
